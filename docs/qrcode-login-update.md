# 扫码登录功能更新说明

## 更新内容

已完善扫码登录逻辑，现在后端返回token参数，前端使用qrcode库生成二维码，并使用token进行轮询。

## 主要变更

### 1. API接口更新
- 使用 `V1OpenApiUserLoginScanQrcode()` 获取登录token（无需参数，直接返回token字符串）
- 使用 `V1OpenApiUserLoginScanLoginStatus({ token })` 查询登录状态
- 移除了WebSocket依赖，改用轮询机制
- 修复了API调用的参数问题和返回值处理

### 2. 轮询机制
- 每3秒轮询一次登录状态
- 出错时5秒后重试
- 二维码过期时自动停止轮询
- 登录成功时立即停止轮询

### 3. 二维码生成逻辑
- 后端返回token字符串，前端使用qrcode库生成二维码图片
- 二维码内容包含：token、登录类型、设备IP、时间戳等信息
- 使用toDataURL生成base64格式的二维码图片
- 设置5分钟过期时间，自动清理资源

### 4. 状态处理
- 支持多种状态字段名：`status`、`loginStatus`、`state`、`result`
- 支持多种状态值：`waiting`、`success`、`expired`
- 兼容不同的API响应格式，遍历result对象查找状态信息
- 自动处理用户信息映射，支持多种用户信息字段名

## 使用流程

1. 用户选择"扫码登录"标签页
2. 系统调用 `V1OpenApiUserLoginScanQrcode()` 获取token
3. 前端使用qrcode库生成包含token信息的二维码
4. 开始轮询 `V1OpenApiUserLoginScanLoginStatus({ token })` 查询状态
5. 用户扫码后，移动端确认登录
6. 轮询检测到登录成功，触发登录事件
7. 自动跳转到首页

## 技术细节

### 轮询间隔
- 正常轮询：3秒
- 错误重试：5秒
- 过期时间：5分钟

### 数据格式兼容
- 二维码内容为JSON格式，包含token、类型、设备IP、时间戳
- 使用qrcode库生成标准的二维码图片
- 支持自定义二维码样式（颜色、大小、边距）

### 错误处理
- 网络错误时继续轮询
- 二维码生成失败时显示错误提示
- 过期时提供刷新功能

## 注意事项

1. **Token获取**：后端API `V1OpenApiUserLoginScanQrcode()` 直接返回token字符串，无需通过`.result`属性访问

2. **二维码生成**：前端使用qrcode库生成二维码，内容为包含token等信息的JSON字符串

3. **状态映射**：登录状态检查支持多种字段名（status、loginStatus、state、result），用户信息支持多种字段名（userInfo、user、data）

4. **性能优化**：轮询机制在组件卸载时会自动清理，避免内存泄漏

## 问题修复

### API调用和返回值处理错误
**问题1**：`V1OpenApiUserLoginScanQrcode` 不接受参数，但之前传入了width和height参数
**问题2**：错误地使用了`.result`属性访问返回值

**原因**：
- API定义中该接口无需参数
- 后端直接返回token字符串，不是包装在result属性中

**解决方案**：
- 移除API调用时的参数
- 直接使用API返回的token字符串，无需通过`.result`访问
- 在前端生成二维码时设置尺寸和样式
- 使用qrcode库的配置选项控制二维码外观

## 后续优化建议

1. 根据实际API响应格式调整数据解析逻辑
2. 可考虑添加扫码进度提示（已扫码、待确认等状态）
3. 可添加扫码失败的重试机制
4. 可优化轮询间隔的动态调整
5. 可添加二维码刷新的动画效果
6. 可考虑添加二维码的自定义样式配置

## 技术实现细节

### 二维码内容格式
```json
{
  "token": "后端返回的登录token",
  "type": "login",
  "deviceIp": "设备IP地址",
  "timestamp": "生成时间戳"
}
```

### 轮询状态处理
- 遍历API返回的result对象的所有属性
- 查找包含状态信息的子对象
- 支持多种状态字段名和值的兼容性处理
- 自动提取用户信息并触发登录事件
